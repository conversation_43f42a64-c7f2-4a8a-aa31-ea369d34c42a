"""
Data models for generate-llms.
"""

from dataclasses import dataclass, field
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime


@dataclass
class CodeSnippet:
    """Represents a code snippet with metadata."""
    
    content: str
    language: Optional[str] = None
    start_line: Optional[int] = None
    end_line: Optional[int] = None
    context_before: Optional[str] = None
    context_after: Optional[str] = None
    heading: Optional[str] = None
    file_path: Optional[Path] = None


@dataclass
class ParsedContent:
    """Represents parsed content from a documentation file."""
    
    file_path: Path
    file_type: str
    encoding: str
    snippets: List[CodeSnippet] = field(default_factory=list)
    raw_content: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    error: Optional[str] = None


@dataclass
class ProcessingConfig:
    """Configuration for document processing."""

    input_directory: Path
    output_file: Path
    mode: str = "full_context"  # "full_context" or "snippets_only"
    max_depth: Optional[int] = None
    languages: Optional[List[str]] = None
    exclude_patterns: Optional[List[str]] = None
    llm_rewrite_enabled: bool = False
    llm_backend: str = "ollama"
    llm_model: str = "llama2:7b"
    llm_temperature: float = 0.3
    llm_max_tokens: int = 500

    # Code filtering settings
    enable_code_filtering: bool = False  # Filter out non-meaningful code (HTML/CSS without logic)

    # Custom system prompt for LLM enhancement
    custom_system_prompt: Optional[str] = None  # Custom system prompt to override default

    # Context chunking settings
    enable_chunking: bool = False  # Enable intelligent context chunking for large content
    max_chunk_tokens: int = 4000  # Maximum tokens per chunk (default for most models)
    chunk_overlap_tokens: int = 200  # Token overlap between chunks for context continuity

    # LM Studio specific settings
    lmstudio_base_url: str = "http://localhost:1234/v1"
    lmstudio_timeout: int = 60
    config_file: Optional[Path] = None
    verbose: bool = False
    dry_run: bool = False

    # Project metadata (inspired by Context7)
    project_title: Optional[str] = None
    project_description: Optional[str] = None
    project_rules: Optional[List[str]] = None
    include_folders: Optional[List[str]] = None

    # File type support
    supported_extensions: List[str] = field(default_factory=lambda: [
        ".md", ".mdx", ".html", ".rst", ".ipynb", ".adoc"
    ])

    # Context7-inspired default exclusions
    default_exclude_files: List[str] = field(default_factory=lambda: [
        "CHANGELOG.md", "changelog.md", "CHANGELOG.mdx", "changelog.mdx",
        "LICENSE.md", "license.md", "LICENSE", "LICENSE.txt",
        "CODE_OF_CONDUCT.md", "code_of_conduct.md",
        "CONTRIBUTING.md", "contributing.md",
        ".gitignore", ".gitattributes", ".github",
    ])

    default_exclude_folders: List[str] = field(default_factory=lambda: [
        "*archive*", "*archived*", "old", "docs/old", "*deprecated*", "*legacy*",
        "*previous*", "*outdated*", "*superseded*",
        "i18n/zh*", "i18n/es*", "i18n/fr*", "i18n/de*", "i18n/ja*", "i18n/ko*",
        "i18n/ru*", "i18n/pt*", "i18n/it*", "i18n/ar*", "i18n/hi*", "i18n/tr*",
        "i18n/nl*", "i18n/pl*", "i18n/sv*", "i18n/vi*", "i18n/th*",
        "zh-cn", "zh-tw", "zh-hk", "zh-mo", "zh-sg",
        "node_modules", ".git", ".vscode", ".idea", "__pycache__",
        "build", "dist", "target", "bin", "obj", ".next", ".nuxt",
        "coverage", ".coverage", ".pytest_cache", ".tox",
        "vendor", "third_party", "external",
    ])

    # Performance settings
    batch_size: int = 100
    max_memory_mb: int = 1000
    enable_parallel: bool = True
    max_workers: int = 4


@dataclass
class ProcessingResult:
    """Result of document processing operation."""
    
    total_files_scanned: int
    files_with_snippets: int
    excluded_files: int
    total_snippets: int
    snippets_by_language: Dict[str, int] = field(default_factory=dict)
    estimated_tokens: int = 0
    estimated_reading_time_minutes: int = 0
    trust_score: float = 0.0
    completeness_score: float = 0.0
    code_coverage_percentage: float = 0.0
    processing_time_seconds: float = 0.0
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    # Generation metadata
    timestamp: datetime = field(default_factory=datetime.now)
    tool_version: str = "1.0.0"
    scan_depth: Optional[int] = None
    
    def to_metadata_dict(self) -> Dict[str, Any]:
        """Convert to metadata dictionary for JSON output."""
        return {
            "generation": {
                "timestamp": self.timestamp.isoformat(),
                "tool_version": self.tool_version,
                "processing_mode": "full_context" if self.estimated_reading_time_minutes > 0 else "snippets_only",
                "llm_rewrite_enabled": False,  # Will be updated by processor
            },
            "source": {
                "root_path": str(Path.cwd()),  # Will be updated by processor
                "total_files_scanned": self.total_files_scanned,
                "files_with_snippets": self.files_with_snippets,
                "excluded_files": self.excluded_files,
                "scan_depth": self.scan_depth,
            },
            "content_metrics": {
                "total_snippets": self.total_snippets,
                "snippets_by_language": self.snippets_by_language,
                "estimated_tokens": self.estimated_tokens,
                "estimated_reading_time_minutes": self.estimated_reading_time_minutes,
            },
            "quality_indicators": {
                "trust_score": self.trust_score,
                "completeness_score": self.completeness_score,
                "code_coverage_percentage": self.code_coverage_percentage,
            },
            "processing": {
                "processing_time_seconds": self.processing_time_seconds,
                "errors": self.errors,
                "warnings": self.warnings,
            }
        }
